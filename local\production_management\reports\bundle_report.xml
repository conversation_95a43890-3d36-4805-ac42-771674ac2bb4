<?xml version="1.0" encoding="UTF-8"?>
<odoo>

  <!-- QWeb Template (unchanged) -->
  <template id="report_bundle_qr">
    <t t-call="web.html_container">
      <t t-foreach="docs" t-as="bundle">
        <div class="page" style="padding:20px; font-family:Arial;">
          <h2>Bundle No: <t t-out="bundle.bundle_no or '---'"/></h2>

          <t t-if="bundle.order_line_id">
            <p>Order: <t t-out="bundle.order_line_id.order_id.name"/></p>
          </t>

          <p>Size: <t t-out="bundle.size or '-'"/></p>
          <p>Quantity: <t t-out="bundle.qty or 0"/></p>

          <t t-if="bundle.bundle_no">
            <div style="margin-top:10px;">
              <img t-att-src="'/report/barcode/QR/%s' % bundle.bundle_no"
                   style="width:100px;height:100px;" alt="QR Code"/>
            </div>
          </t>
        </div>
      </t>
    </t>
  </template>

  <!-- PDF Report definition -->
  <record id="action_bundle_qr_report" model="ir.actions.report">
    <field name="name">Print Bundle Ticket (PDF)</field>
    <field name="model">production.bundle</field>
    <field name="report_type">qweb-pdf</field>
    <field name="report_name">production_management.report_bundle_qr</field>
    <field name="report_file">production_management.report_bundle_qr</field>
    <field name="print_report_name">'Bundle_' + (object.bundle_no or 'unknown')</field>
    <field name="binding_model_id" ref="model_production_bundle"/>
    <field name="binding_type">report</field>
  </record>
  
  <!-- HTML Report definition (for viewing in browser) -->
  <record id="action_bundle_qr_report_html" model="ir.actions.report">
    <field name="name">View Bundle Ticket</field>
    <field name="model">production.bundle</field>
    <field name="report_type">qweb-html</field>
    <field name="report_name">production_management.report_bundle_qr</field>
    <field name="report_file">production_management.report_bundle_qr</field>
    <field name="binding_model_id" ref="model_production_bundle"/>
    <field name="binding_type">report</field>
  </record>

</odoo>