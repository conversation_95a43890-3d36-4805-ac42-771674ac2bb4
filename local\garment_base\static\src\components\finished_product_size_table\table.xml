<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="garment.FinishedProductSizeTable">
        <t t-if="props.record.data[props.name]">
            <!-- Controls above table -->
            <div class="d-flex justify-content-between mb-2" t-if="!props.readonly">
                <!-- Left group -->
                <div class="d-flex">
                    <button type="button" class="btn btn-primary btn-sm" t-on-click="addRow">
                        Add Row
                    </button>
                    <button type="button" class="btn btn-primary btn-sm ms-1" t-on-click="addColumn">
                        Add Column
                    </button>
                </div>
                <!-- Right group -->
                <div class="d-flex">
                    <button type="button" class="btn btn-primary btn-sm me-1" t-on-click="() => saveTemplate()">
                        Save Template
                    </button>
                    <button type="button" class="btn btn-primary btn-sm" t-on-click="() => useTemplate()">
                        Use Template
                    </button>
                </div>
            </div>

            <!-- Table -->
            <div class="table-wrapper">
                <table class="table table-sm bold-first table-bordered" t-att-class="classFromDecoration">
                    <thead>
                        <tr>
                            <t t-foreach="state.rows[0]" t-as="row" t-key="row_index" t-index="row_index">
                                <td class="p-0 text-center">
                                    <button type="button" class="btn btn-sm btn-outline-danger" t-on-click="() => deleteColumn(row_index)" t-if="state.rows[0].length > 2 &amp;&amp; !props.readonly">
                                        -
                                    </button>
                                </td>
                            </t>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="state.rows" t-as="row" t-key="row_index" t-index="row_index">
                            <tr>
                                <t t-foreach="Object.values(row)" t-as="cell" t-key="cell_index" t-index="cell_index">
                                    <td t-att-contenteditable="!props.readonly" t-on-blur="(event) => onCellBlur(event, row_index, cell_index)">
                                        <t t-esc="cell"/>
                                    </td>
                                </t>
                                <t t-if="state.rows.length > 2 &amp;&amp; !props.readonly">
                                    <td class="delete-col text-center">
                                        <button type="button" class="btn btn-sm btn-outline-danger" t-on-click="() => deleteRow(row_index)">
                                            Delete
                                        </button>
                                    </td>
                                </t>
                            </tr>
                        </t>
                    </tbody>
                </table>
            </div>
        </t>
    </t>
</templates>
