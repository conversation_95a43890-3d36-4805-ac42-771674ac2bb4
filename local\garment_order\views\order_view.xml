<odoo>
    <record id="view_garment_order_tree" model="ir.ui.view">
        <field name="name">garment.order.tree</field>
        <field name="model">garment.order</field>
        <field name="arch" type="xml">
            <tree string="Garment Orders" create="false" js_class="order_create_button">
                <field name="name"/>
                <field name="order_number"/>
                <field name="shape"/>
                <field name="quantity"/>
                <field name="state"/>
                <field name="receiving_company"/>
                <!-- <field name="department_id"/> -->
                <field name="receiving_date"/>
                <field name="cutting_date"/>
                <field name="published_by"/>
                <field name="issuing_date"/>
            </tree>
        </field>
    </record>

    <!-- View-only form -->
    <record id="view_garment_order_form_view" model="ir.ui.view">
        <field name="name">garment.order.form.view</field>
        <field name="model">garment.order</field>
        <field name="arch" type="xml">
            <form string="Garment Order" create="false" delete="false" edit="false">
                <header>
                    <!-- <field name="state" widget="statusbar" statusbar_visible="new,in_progress,eliminated"/>
                    <button name="action_open_edit_form" type="object" string="Edit" class="oe_highlight"/>
                    <div class="o_dropdown">
                        <button class="btn btn-primary btn-sm dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fa fa-download"/>
                            Export
                        </button>
                        <div class="dropdown-menu" role="menu">
                            <button name="action_export_pdf" type="object" class="dropdown-item" string="Export PDF" data-hotkey="p"/>
                            <button name="action_export_xlsx" type="object" class="dropdown-item" string="Export Excel" data-hotkey="e"/>
                        </div>
                    </div>
                    <button name="action_mark_ready_for_production" type="object" string="Mark as Ready for Production" class="btn-primary"/>
                    <button name="action_mark_discontinued" type="object" string="Mark as Discontinued" class="btn-primary"/>
                    <button name="action_delete_record" type="object" string="Delete" class="btn-danger" confirm="Are you sure you want to delete this record? This action cannot be undone."/> -->
                </header>
                <sheet>
                    <group string="Basic Info" col="3">
                        <field name="sample_id"/>
                    </group>
                    <group string="Basic Info" col="3">
                        <group>
                            <field name="name" required="1"/>
                            <field name="unit_price" required="1"/>
                            <field name="issuing_company"/>
                            <field name="receiving_company"/>
                        </group>
                        <group>
                            <field name="order_number" required="1"/>
                            <field name="shape"/>
                            <field name="issuing_company_phone"/>
                            <field name="receiving_company_phone"/>
                        </group>
                        <group>
                            <!-- <field name="production_id"/> -->
                            <field name="quantity" required="1"/>
                            <field name="color"/>
                            <field name="remark" widget="html"/>
                            <field name="state"/>
                            <field name="published_by"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Order Detail">
                            <group string="Specification Detail">
                                <field name="specification_detail" widget="specification_detail_table" string="" readonly="1"/>
                            </group>
                            <group string="Material Detail">
                                <field name="material_detail" widget="material_detail_table" string="" readonly="1"/>
                            </group>
                            <group string="Remarks">
                                <field name="remark" string="" readonly="1"/>
                            </group>
                            <group string="Image Details">
                                <field name="image_detail" widget="image" options="{'preview_image': 'image_detail'}" readonly="1" string=""/>
                            </group>
                            <group string="Related Documents">
                                <field name="related_document" filename="related_document_filename" readonly="1" string=""/>
                            </group>
                        </page>
                        <page string="Material Issuance">

                        </page>
                        <page string="Order Quotation">

                        </page>
                        <page string="Order Progress">

                        </page>
                        <page string="Related Samples">
                        </page>
                        <page string="Related Productions">
                        </page>

                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Edit form -->
    <record id="view_garment_order_form_edit" model="ir.ui.view">
        <field name="name">garment.order.form.edit</field>
        <field name="model">garment.order</field>
        <field name="arch" type="xml">
            <form string="Edit Garment Order" create="false">
                <header>
                    <!-- <field name="state" widget="statusbar" statusbar_visible="new,in_progress,eliminated"/> -->
                </header>
                <sheet>
                    <group string="Basic Info" colspan="4">
                        <field name="sample_id"/>
                        <!-- <field name="production_id"/> -->
                    </group>

                    <group>
                        <group>
                            <field name="name" required="1"/>
                            <field name="unit_price" required="1"/>
                            <field name="issuing_company"/>
                            <field name="receiving_company"/>

                        </group>
                        <group>
                            <field name="order_number" required="1"/>
                            <field name="shape"/>
                            <field name="issuing_company_phone"/>
                            <field name="receiving_company_phone"/>
                        </group>
                        <group>
                            <field name="quantity" required="1"/>
                            <field name="color"/>
                            <field name="cutting_date"/>
                            <field name="department_id"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Specification Detail">
                            <group>
                                <field name="specification_detail" widget="specification_detail_table" string=""/>
                            </group>
                        </page>
                        <page string="Material Detail">
                            <group>
                                <field name="material_detail" widget="material_detail_table" string=""/>
                            </group>
                        </page>
                        <page string="Other Costs">
                            <group>
                                <field name="other_cost" widget="other_cost_table" string=""/>
                            </group>
                        </page>
                        <page string="Progress Detail">
                            <group>
                                <field name="progress_detail" widget="progress_template" string=""/>
                            </group>
                        </page>
                    </notebook>
                    <group string="Attachments">
                        <field name="image_detail" widget="image" options="{'preview_image': 'image_detail'}"/>
                        <field name="related_document" filename="related_document_filename"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action for opening edit form in dialog -->
    <record id="action_garment_order_edit" model="ir.actions.act_window">
        <field name="name">Edit Garment Order</field>
        <field name="res_model">garment.order</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_garment_order_form_edit"/>
        <field name="target">new</field>
    </record>

    <record id="action_garment_order" model="ir.actions.act_window">
        <field name="name">Garment Orders</field>
        <field name="res_model">garment.order</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{
            'form_view_ref': 'garment_order.view_garment_order_form_view',
            'create_view_ref': 'garment_order.view_garment_order_form_edit'
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new Garment Order
            </p>
        </field>
    </record>
</odoo>