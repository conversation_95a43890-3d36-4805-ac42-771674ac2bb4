FROM python:3.10
# Environment variables
ENV LANG C.UTF-8
ENV LC_ALL C.UTF-8
ENV PYTHONUNBUFFERED 1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    wget \
    nodejs \
    npm \
    libldap2-dev \
    libsasl2-dev \
    libxml2-dev \
    libxslt1-dev \
    libzip-dev \
    libjpeg-dev \
    libpq-dev \
    build-essential \
    python3-dev \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Install LESS compiler for Odoo frontend assets
RUN npm install -g less less-plugin-clean-css

# Set work directory
WORKDIR /opt/odoo

# Copy entrypoint script and make executable
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# Copy source code
COPY ./src/odoo ./odoo
COPY ./local ./local

# Copy config & requirements
COPY odoo.cfg /etc/odoo/odoo.conf
COPY requirements.txt .

# Install python dependencies
RUN pip install --upgrade pip && pip install -r requirements.txt

# Set permissions
RUN adduser --disabled-password --gecos "" odoo \
 && chown -R odoo:odoo /opt/odoo

USER odoo
WORKDIR /opt/odoo/odoo

ENTRYPOINT ["/entrypoint.sh"]

CMD ["python3", "/opt/odoo/odoo/odoo-bin", "-c", "/etc/odoo/odoo.conf"]
