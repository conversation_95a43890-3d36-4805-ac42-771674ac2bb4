<odoo>
    <record id="view_production_cost_tree" model="ir.ui.view">
        <field name="name">production.cost.tree</field>
        <field name="model">production.cost</field>
        <field name="arch" type="xml">
            <tree string="Production Costs" editable="bottom">
                <field name="cost_type"/>
                <field name="amount" sum="Total"/>
                <field name="note"/>
            </tree>
        </field>
    </record>

    <record id="view_production_cost_form" model="ir.ui.view">
        <field name="name">production.cost.form</field>
        <field name="model">production.cost</field>
        <field name="arch" type="xml">
            <form string="Production Cost">
                <sheet>
                    <group>
                        <group>
                            <field name="cost_type"/>
                        </group>
                        <group>
                            <field name="amount"/>
                        </group>
                    </group>
                    <field name="note" placeholder="Add notes here..."/>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_production_cost" model="ir.actions.act_window">
        <field name="name">Costs</field>
        <field name="res_model">production.cost</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="menu_production_cost" name="Production Costs"
              parent="menu_production_management_root" action="action_production_cost" sequence="65"/>
</odoo> 