<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Production Order Sequence -->
        <record id="seq_production_order" model="ir.sequence">
            <field name="name">Production Order</field>
            <field name="code">production.order</field>
            <field name="prefix">PRO/</field>
            <field name="padding">4</field>
            <field name="company_id" eval="False"/>
        </record>
    </data>
</odoo> 