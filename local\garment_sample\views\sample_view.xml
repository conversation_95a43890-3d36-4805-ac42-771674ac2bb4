<odoo>
    <record id="view_garment_sample_tree" model="ir.ui.view">
        <field name="name">garment.sample.tree</field>
        <field name="model">garment.sample</field>
        <field name="arch" type="xml">
            <tree string="Garment Samples" create="false" js_class="custom_create_button">
                <field name="name" string="Sample Name"/>
                <field name="number" string="Sample number"/>
                <field name="quantity"/>
                <field name="shape"/>
                <field name="color"/>
                <field name="state" decoration-info="state == 'new'" decoration-success="state == 'in_progress'" decoration-danger="state == 'eliminated'"/>
                <field name="client"/>
                <field name="brand"/>
                <field name="department_id" string="Department"/>
                <field name="published_by"/>
                <field name="development_date"/>
            </tree>
        </field>
    </record>

    <!-- View-only form -->
    <record id="view_garment_sample_form_view" model="ir.ui.view">
        <field name="name">garment.sample.form.view</field>
        <field name="model">garment.sample</field>
        <field name="arch" type="xml">
            <form string="Garment Sample" create="false" delete="false" edit="false">
                <header>
                    <field name="state" widget="statusbar" statusbar_visible="new,in_progress,eliminated"/>
                    <button name="action_open_edit_form" type="object" string="Edit" class="oe_highlight"/>
                    <div class="o_dropdown">
                        <button class="btn btn-primary btn-sm dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fa fa-download"/>
                            Export
                        </button>
                        <div class="dropdown-menu" role="menu">
                            <button name="action_export_pdf" type="object" class="dropdown-item" string="Export PDF" data-hotkey="p"/>
                            <button name="action_export_xlsx" type="object" class="dropdown-item" string="Export Excel" data-hotkey="e"/>
                        </div>
                    </div>
                    <button name="action_mark_ready_for_production" type="object" string="Mark as Ready for Production" class="btn-primary"/>
                    <button name="action_mark_discontinued" type="object" string="Mark as Discontinued" class="btn-primary"/>
                    <button name="action_delete_record" type="object" string="Delete" class="btn-danger" confirm="Are you sure you want to delete this record? This action cannot be undone."/>
                </header>
                <sheet>
                    <group string="Basic Info" col="3">
                        <group>
                            <field name="name" readonly="1"/>
                            <field name="client" readonly="1"/>
                            <field name="designer" readonly="1"/>
                            <field name="quantity" readonly="1"/>
                            <field name="published_by" readonly="1"/>
                            <field name="update_date" readonly="1"/>
                        </group>
                        <group>
                            <field name="shape" readonly="1"/>
                            <field name="brand" readonly="1"/>
                            <field name="pattern_maker" readonly="1"/>
                            <field name="pattern_size" readonly="1"/>
                            <field name="phone_number" readonly="1"/>
                        </group>
                        <group>
                            <field name="color" readonly="1"/>
                            <field name="quotation" readonly="1"/>
                            <field name="pattern_drafter" readonly="1"/>
                            <field name="department_id" readonly="1"/>
                            <field name="development_date" readonly="1"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Sample Detail">
                            <group string="Finished Product Size">
                                <field name="finished_product_size" widget="finished_product_size_table" string="" readonly="1"/>
                            </group>
                            <group string="Material Detail">
                                <field name="material_detail" widget="material_detail_table" string="" readonly="1"/>
                            </group>
                            <group string="Process Requirements">
                                <field name="process_requirements" string="" readonly="1"/>
                            </group>
                            <group string="Remarks">
                                <field name="remark" string="" readonly="1"/>
                            </group>
                            <group string="Image Details">
                                <field name="image_detail" widget="image" options="{'preview_image': 'image_detail'}" readonly="1" string=""/>
                            </group>
                            <group string="Related Documents">
                                <field name="related_document" filename="related_document_filename" readonly="1" string=""/>
                            </group>
                        </page>
                        <page string="Material Issuance">

                        </page>
                        <page string="Sample Quotation">
                            <widget name="sample_cost_summary_table" string="Sample Cost Summary"/>
                            <group string="Process Cost">
                                <field name="process_table" widget="process_table" string="" readonly="1" hide_fields="['multiplier']"/>
                            </group>
                            <group string="Other Cost">
                                <field name="other_cost" widget="other_cost_table" string="" readonly="1"/>
                            </group>
                        </page>
                        <page string="Sample Progress">
                            <field name="progress_detail" widget="progress_template" string=""/>
                        </page>
                        <!-- <page string="Sample Warehousing">
                        </page>
                        <page string="Related Orders">
                        </page>
                        <page string="Related Purchases">
                        </page>
                        <page string="Related Productions">
                        </page> -->
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Edit form -->
    <record id="view_garment_sample_form_edit" model="ir.ui.view">
        <field name="name">garment.sample.form.edit</field>
        <field name="model">garment.sample</field>
        <field name="arch" type="xml">
            <form string="Edit Garment Sample" create="false">
                <header>
                    <field name="state" widget="statusbar" statusbar_visible="new,in_progress,eliminated"/>
                </header>
                <sheet>
                    <group string="Basic Info" colspan="4">
                        <group>
                            <field name="name" required="1"/>
                            <field name="number"/>
                            <field name="department_id"/>
                            <field name="phone_number"/>
                        </group>
                        <group>
                            <field name="client"/>
                            <field name="brand"/>
                            <field name="published_by"/>
                            <field name="development_date"/>
                        </group>
                    </group>

                    <group string="Sample Details" colspan="5">
                        <group>
                            <field name="shape"/>
                            <field name="color"/>
                            <field name="quotation"/>
                            <field name="quantity"/>
                            <field name="designer"/>
                        </group>
                        <group>
                            <field name="pattern_maker"/>
                            <field name="pattern_drafter"/>
                            <field name="pattern_size"/>
                            <field name="process_requirements"/>
                            <field name="remark"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Finished Product Size">
                            <group>
                                <field name="finished_product_size" widget="finished_product_size_table" string=""/>
                            </group>
                        </page>
                        <page string="Material Detail">
                            <group>
                                <field name="material_detail" widget="material_detail_table" string=""/>
                            </group>
                        </page>
                        <page string="Process table">
                            <group>
                                <field name="process_table" widget="process_table" string=""/>
                            </group>
                        </page>
                        <page string="Other Costs">
                            <group>
                                <field name="other_cost" widget="other_cost_table" string=""/>
                            </group>
                        </page>
                    </notebook>
                    <group string="Attachments">
                        <field name="image_detail" widget="image" options="{'preview_image': 'image_detail'}"/>
                        <field name="related_document" filename="related_document_filename"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action for opening edit form in dialog -->
    <record id="action_garment_sample_edit" model="ir.actions.act_window">
        <field name="name">Edit Garment Sample</field>
        <field name="res_model">garment.sample</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_garment_sample_form_edit"/>
        <field name="target">new</field>
    </record>

    <record id="action_garment_sample" model="ir.actions.act_window">
        <field name="name">Garment Samples</field>
        <field name="res_model">garment.sample</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{
            'form_view_ref': 'garment_sample.view_garment_sample_form_view',
            'create_view_ref': 'garment_sample.view_garment_sample_form_edit'
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new Garment Sample
            </p>
        </field>
    </record>
</odoo>