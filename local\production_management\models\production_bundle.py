from odoo import models, fields, api

class ProductionBundle(models.Model):
    _name = 'production.bundle'
    _description = 'Production Bundle'

    bundle_no = fields.Char('Bundle Number', required=True, unique=True)
    size = fields.Char('Size', required=True)
    qty = fields.Integer('Quantity', required=True)
    ticket_printed = fields.Bo<PERSON>an('Ticket Printed', default=False)
    
    # Relationships
    order_line_id = fields.Many2one('production.order.line', string='Order Line', required=True, ondelete='cascade')
    worker_entry_id = fields.Many2one('production.worker.entry', string='Worker Entry') 