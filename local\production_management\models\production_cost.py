from odoo import models, fields, api

class ProductionCost(models.Model):
    _name = 'production.cost'
    _description = 'Production Cost'

    cost_type = fields.Selection([
        ('export_tax', 'Export Tax'),
        ('vat', 'VAT'),
        ('water', 'Water'),
        ('bonded', 'Bonded'),
        ('utilities', 'Utilities'),
        ('misc_fees', 'Misc Fees'),
        ('shipping', 'Shipping'),
        ('management', 'Management'),
        ('transport', 'Transport'),
        ('other', 'Other'),
        ('other_summary', 'Other Costs Summary'),
        ('total_display', 'Total Display')
    ], string='Cost Type', required=True)
    
    amount = fields.Float('Amount', digits=(12, 2))
    note = fields.Text('Note')
    
    # Relationships
    order_id = fields.Many2one('production.order', string='Production Order', required=True, ondelete='cascade') 