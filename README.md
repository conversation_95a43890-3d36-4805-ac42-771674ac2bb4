# odoo-final-project



## Getting started
- Python version: >= 3.10 & < 3.12

- Newest version of Python cannot be used for Odoo Enterprise 17
## Set up project for Window
- Run the commands:
```
.\bin\windows\setup.bat
python -m venv .venv
./.venv/Scripts/activate
pip install -r bin/windows/requirements.txt
```

- Download [odoo folder](https://husteduvn.sharepoint.com/:u:/s/Odoo/EcYVwwCOuktEi6cWveSw_y4BA2jHsGelRilytyeIT8WVEw?e=PN0FDw)
- Copy it to /src
## Config your own Odoo instance

- Modify the [ configuration file odoo-local.cfg](odoo-local.cfg)

## Run
```
.\bin\windows\odoo.bat
```

## Master Key
xyef-4khz-j3mu